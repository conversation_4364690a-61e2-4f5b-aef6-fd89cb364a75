terraform {
  required_providers {
    launchdarkly = {
      source  = "launchdarkly/launchdarkly"
      version = "~> 2.24.0"
    }
  }
}

resource "launchdarkly_project" "propaga" {
  key  = "propaga"
  name = "Propaga"

  tags = ["terraform"]

  environments {
    name  = "Development"
    key   = "develop"
    color = "7B42BC"
    tags  = ["terraform"]
  }

  environments {
    name  = "Staging"
    key   = "staging"
    color = "F5A623"
    tags  = ["terraform"]
  }

  environments {
    name  = "Production"
    key   = "production"
    color = "417505"
    tags  = ["terraform"]
  }
}

resource "launchdarkly_feature_flag" "update_payment_date_delivery_rabbit" {
  project_key = launchdarkly_project.propaga.key
  key         = "update-payment-date-delivery-rabbit"
  name        = "Update Payment Date on Delivery - Rabbit"
  description = "Enable/disable update payment date on delivery for Rabbit"

  variation_type = "boolean"
  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }
  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "payment", "rabbit"]
}

resource "launchdarkly_feature_flag" "update_payment_date_delivery_dupplo" {
  project_key = launchdarkly_project.propaga.key
  key         = "update-payment-date-delivery-dupplo"
  name        = "Update Payment Date on Delivery - Dupplo"
  description = "Enable/disable update payment date on delivery for Dupplo"

  variation_type = "boolean"
  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }
  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "payment", "dupplo"]
}

resource "launchdarkly_feature_flag" "user_validation_process_rabbit" {
  project_key = launchdarkly_project.propaga.key
  key         = "user-validation-process-rabbit"
  name        = "User Validation Process - Rabbit"
  description = "Enable/disable user validation process for Rabbit"

  variation_type = "boolean"
  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }
  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "validation", "rabbit"]
}

resource "launchdarkly_feature_flag" "user_validation_process_dupplo" {
  project_key = launchdarkly_project.propaga.key
  key         = "user-validation-process-dupplo"
  name        = "User Validation Process - Dupplo"
  description = "Enable/disable user validation process for Dupplo"

  variation_type = "boolean"
  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }
  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "validation", "dupplo"]
}

resource "launchdarkly_feature_flag" "user_validation_process_chiper" {
  project_key = launchdarkly_project.propaga.key
  key         = "user-validation-process-chiper"
  name        = "User Validation Process - Chiper"
  description = "Enable/disable user validation process for Chiper"

  variation_type = "boolean"
  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }
  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "validation", "chiper"]
}

resource "launchdarkly_feature_flag" "transaction_confirmation_rabbit" {
  project_key = launchdarkly_project.propaga.key
  key         = "transaction-confirmation-rabbit"
  name        = "Transaction Confirmation - Rabbit"
  description = "Enable/disable transaction confirmation for Rabbit"

  variation_type = "boolean"
  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }
  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "transaction", "rabbit"]
}

resource "launchdarkly_feature_flag" "transaction_confirmation_chiper" {
  project_key = launchdarkly_project.propaga.key
  key         = "transaction-confirmation-chiper"
  name        = "Transaction Confirmation - Chiper"
  description = "Enable/disable transaction confirmation for Chiper"

  variation_type = "boolean"
  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }
  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "transaction", "chiper"]
}

resource "launchdarkly_feature_flag" "first_transaction_without_interests_rabbit" {
  project_key = launchdarkly_project.propaga.key
  key         = "first-transaction-without-interests-rabbit"
  name        = "First Transaction Without Interests - Rabbit"
  description = "Enable/disable first transaction without interests for Rabbit"

  variation_type = "boolean"
  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }
  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "transaction", "rabbit"]
}

resource "launchdarkly_feature_flag" "first_transaction_without_interests_chiper" {
  project_key = launchdarkly_project.propaga.key
  key         = "first-transaction-without-interests-chiper"
  name        = "First Transaction Without Interests - Chiper"
  description = "Enable/disable first transaction without interests for Chiper"

  variation_type = "boolean"
  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }
  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "transaction", "chiper"]
}

resource "launchdarkly_feature_flag" "paid_transaction_notification_rabbit" {
  project_key = launchdarkly_project.propaga.key
  key         = "paid-transaction-notification-rabbit"
  name        = "Paid Transaction Notification - Rabbit"
  description = "Enable/disable paid transaction notification for Rabbit"

  variation_type = "boolean"
  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }
  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "notification", "rabbit"]
}

resource "launchdarkly_feature_flag" "paid_transaction_notification_chiper" {
  project_key = launchdarkly_project.propaga.key
  key         = "paid-transaction-notification-chiper"
  name        = "Paid Transaction Notification - Chiper"
  description = "Enable/disable paid transaction notification for Chiper"

  variation_type = "boolean"
  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }
  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "notification", "chiper"]
}

resource "launchdarkly_feature_flag" "blocked_users_with_validator_rabbit" {
  project_key = launchdarkly_project.propaga.key
  key         = "blocked-users-with-validator-rabbit"
  name        = "Blocked Users with Validator - Rabbit"
  description = "Enable/disable blocked users with validator for Rabbit"

  variation_type = "boolean"
  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }
  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "validation", "rabbit"]
}

resource "launchdarkly_feature_flag" "blocked_users_with_validator_chiper" {
  project_key = launchdarkly_project.propaga.key
  key         = "blocked-users-with-validator-chiper"
  name        = "Blocked Users with Validator - Chiper"
  description = "Enable/disable blocked users with validator for Chiper"

  variation_type = "boolean"
  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }
  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "validation", "chiper"]
}

resource "launchdarkly_feature_flag" "blocked_users_with_validator_rintin" {
  project_key = launchdarkly_project.propaga.key
  key         = "blocked-users-with-validator-rintin"
  name        = "Blocked Users with Validator - Rintin"
  description = "Enable/disable blocked users with validator for Rintin"

  variation_type = "boolean"
  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }
  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "validation", "rintin"]
}

resource "launchdarkly_feature_flag" "blocked_users_with_validator_scorpion" {
  project_key = launchdarkly_project.propaga.key
  key         = "blocked-users-with-validator-scorpion"
  name        = "Blocked Users with Validator - Scorpion"
  description = "Enable/disable blocked users with validator for Scorpion"

  variation_type = "boolean"
  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }
  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "validation", "scorpion"]
}

resource "launchdarkly_feature_flag" "physical_process_rabbit" {
  project_key = launchdarkly_project.propaga.key
  key         = "physical-process-rabbit"
  name        = "Physical Process - Rabbit"
  description = "Enable/disable physical process for Rabbit"

  variation_type = "boolean"
  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }
  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "process", "rabbit"]
}

resource "launchdarkly_feature_flag" "user_activation_notification_rabbit" {
  project_key = launchdarkly_project.propaga.key
  key         = "user-activation-notification-rabbit"
  name        = "User Activation Notification - Rabbit"
  description = "Enable/disable user activation notification for Rabbit"

  variation_type = "boolean"
  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }
  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "notification", "rabbit"]
}

resource "launchdarkly_feature_flag" "carrot_project" {
  project_key = launchdarkly_project.propaga.key
  key         = "carrot-project"
  name        = "Carrot Project"
  description = "Enable/disable Carrot Project features"

  variation_type = "boolean"
  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }
  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "project", "carrot"]
}

resource "launchdarkly_feature_flag" "clabe_assignation" {
  project_key = launchdarkly_project.propaga.key
  key         = "clabe-assignation"
  name        = "CLABE Assignation"
  description = "Enable/disable CLABE assignation feature"

  variation_type = "boolean"
  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }
  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "payment", "clabe"]
}

resource "launchdarkly_feature_flag" "user_activation_notification_yalo" {
  project_key = launchdarkly_project.propaga.key
  key         = "user-activation-notification-yalo"
  name        = "User Activation Notification - Yalo"
  description = "Enable/disable user activation notification for Yalo"

  variation_type = "boolean"
  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }
  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "notification", "yalo"]
}

resource "launchdarkly_feature_flag" "user_activation_notification_nestle" {
  project_key = launchdarkly_project.propaga.key
  key         = "user-activation-notification-nestle"
  name        = "User Activation Notification - Nestle"
  description = "Enable/disable user activation notification for Nestle"

  variation_type = "boolean"
  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }
  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "notification", "nestle"]
}

resource "launchdarkly_feature_flag" "blocked_users_with_validator_yalo" {
  project_key = launchdarkly_project.propaga.key
  key         = "blocked-users-with-validator-yalo"
  name        = "Blocked Users with Validator - Yalo"
  description = "Enable/disable blocked users with validator for Yalo"

  variation_type = "boolean"
  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }
  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "validation", "yalo"]
}

resource "launchdarkly_feature_flag" "blocked_users_with_validator_nestle" {
  project_key = launchdarkly_project.propaga.key
  key         = "blocked-users-with-validator-nestle"
  name        = "Blocked Users with Validator - Nestle"
  description = "Enable/disable blocked users with validator for Nestle"

  variation_type = "boolean"
  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }
  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "validation", "nestle"]
}

resource "launchdarkly_feature_flag" "oxxo-pay" {
  project_key = launchdarkly_project.propaga.key
  key         = "is-enabled-oxxo-pay-as-payment-method"
  name        = "Oxxo Pay"
  description = "Enable/disable Oxxo Pay as payment method"

  variation_type = "boolean"

  variations {
    value       = true
    name        = "Enabled"
    description = "The feature is enabled"
  }

  variations {
    value       = false
    name        = "Disabled"
    description = "The feature is disabled"
  }

  defaults {
    on_variation  = 0
    off_variation = 1
  }

  tags = ["terraform", "monoapi", "payment", "oxxo"]
}
