module "ecs" {
  source = "../../services/ecs"

  app_name          = "pioner"
  image_repo_url    = "014817073571.dkr.ecr.us-west-2.amazonaws.com/pioneer:${var.image_tag}"
  cpu               = 256
  memory            = 512
  desired_count     = 1
  container_port    = 3004
  vpc_id            = var.vpc_id
  cluster_id        = var.cluster_id
  security_group_id = var.security_group_id
  subnets           = var.subnets
  load_balancer_arn = var.load_balancer_arn
  Environment       = var.environment
  env = [
    {
      "name" : "TZ",
      "value" : "UTC-6"
    },
    {
      "name" : "NODE_ENV",
      "value" : var.node_env
    },
  ]
}
