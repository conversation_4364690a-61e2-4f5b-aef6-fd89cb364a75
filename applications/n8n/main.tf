module "n8n" {
  source = "../../services/target-group"

  port     = 5678
  app_name = "n8n"
  vpc_id   = var.vpc_id
}


resource "aws_alb_listener" "alb_listener" {
  load_balancer_arn = var.load_balancer_arn

  port     = 5678
  protocol = "HTTP"

  default_action {
    type             = "forward"
    target_group_arn = module.n8n.target_group_arn
  }
}

resource "aws_lb_target_group_attachment" "n8n_attachment" {
  target_group_arn = module.n8n.target_group_arn
  target_id        = "i-0160c5b0a7b38d858"
  port             = 5678
}