module "ecs" {
  source = "../../services/ecs"

  app_name          = "docapi"
  image_repo_url    = "014817073571.dkr.ecr.us-west-2.amazonaws.com/doc-api:${var.image_tag}"
  cpu               = 256
  memory            = 512
  desired_count     = 1
  container_port    = 8080
  vpc_id            = var.vpc_id
  cluster_id        = var.cluster_id
  security_group_id = var.security_group_id
  subnets           = var.subnets
  load_balancer_arn = var.load_balancer_arn
  Environment       = var.image_tag
}
