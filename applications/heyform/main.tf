module "heyform" {
  source = "../../services/target-group"

  port     = 9513
  app_name = "heyfor"
  vpc_id   = var.vpc_id
}


resource "aws_alb_listener" "alb_listener" {
  load_balancer_arn = var.load_balancer_arn

  port     = 9513
  protocol = "HTTP"

  default_action {
    type             = "forward"
    target_group_arn = module.heyform.target_group_arn
  }
}

resource "aws_lb_target_group_attachment" "heyform_attachment" {
  target_group_arn = module.heyform.target_group_arn
  target_id        = "i-08b215c019b976c77"
  port             = 9513
}