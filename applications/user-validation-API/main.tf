module "ecs" {
  source = "../../services/ecs"

  app_name          = "valapi"
  image_repo_url    = "014817073571.dkr.ecr.us-west-2.amazonaws.com/user-validation-api:${var.image_tag}"
  cpu               = 256
  memory            = 512
  desired_count     = 1
  container_port    = 3003
  vpc_id            = var.vpc_id
  cluster_id        = var.cluster_id
  security_group_id = var.security_group_id
  subnets           = var.subnets
  load_balancer_arn = var.load_balancer_arn
  Environment       = var.environment
  env = [
    {
      "name" : "NODE_ENV",
      "value" : var.environment
    },
    {
      "name" : "TZ",
      "value" : "UTC-6"
    },
    {
      "name" : "DD_ENV",
      "value" : var.environment
    },
    {
      "name" : "DD_SERVICE",
      "value" : "valapi"
    },
    {
      "name" : "DD_VERSION",
      "value" : "1.3.1"
    },
    {
      "name" : "DD_LOGS_INJECTION",
      "value" : "true"
    },
    {
      "name" : "DD_AGENT_HOST",
      "value" : "dd-ingest.propaga.io"
    },
    {
      "name" : "DD_APPSEC_ENABLED",
      "value" : "true"
    }
  ]
}
