module "ecs" {
  source = "../../services/ecs"

  app_name          = "datdog"
  image_repo_url    = "public.ecr.aws/datadog/agent:latest"
  cpu               = 256
  memory            = 512
  desired_count     = 1
  container_port    = 8126
  vpc_id            = var.vpc_id
  cluster_id        = var.cluster_id
  security_group_id = var.security_group_id
  subnets           = var.subnets
  load_balancer_arn = var.load_balancer_arn
  Environment       = var.image_tag
  env = [
    {
      "name" : "DD_API_KEY",
      "value" : var.datadog_token
    },
    {
      "name" : "ECS_FARGATE",
      "value" : "true"
    },
    {
      "name" : "DD_SITE",
      "value" : "us3.datadoghq.com"
    },
    {
      "name" : "DD_LOGS_ENABLED",
      "value" : "true"
    },
    {
      "name" : "DD_LOGS_CONFIG_CONTAINER_COLLECT_ALL",
      "value" : "true"
    },
    {
      "name" : "SD_BACKEND",
      "value" : "docker"
    }
  ]
}
