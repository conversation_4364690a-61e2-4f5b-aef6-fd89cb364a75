module "sentry" {
  source = "../../services/target-group"

  port     = 9000
  app_name = "sentry"
  vpc_id   = var.vpc_id
}


resource "aws_alb_listener" "alb_listener" {
  load_balancer_arn = var.load_balancer_arn

  port     = 9000
  protocol = "HTTP"

  default_action {
    type             = "forward"
    target_group_arn = module.sentry.target_group_arn
  }
}

resource "aws_lb_target_group_attachment" "sentry_attachment" {
  target_group_arn = module.sentry.target_group_arn
  target_id        = "i-028e57487976e7fa4"
  port             = 9000
}