module "grafana" {
  source = "../../services/target-group"

  port     = 4000
  app_name = "grafan"
  vpc_id   = var.vpc_id
}


resource "aws_alb_listener" "alb_listener" {
  load_balancer_arn = var.load_balancer_arn

  port     = 4000
  protocol = "HTTP"

  default_action {
    type             = "forward"
    target_group_arn = module.grafana.target_group_arn
  }
}

resource "aws_lb_target_group_attachment" "grafana_attachment" {
  target_group_arn = module.grafana.target_group_arn
  target_id        = "i-0f2b6a8881d4f5013"
  port             = 4000
}