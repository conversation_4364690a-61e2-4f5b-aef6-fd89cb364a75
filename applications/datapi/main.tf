module "ecs" {
  source = "../../services/ecs"

  app_name          = "datapi"
  image_repo_url    = "014817073571.dkr.ecr.us-west-2.amazonaws.com/data-api:${var.image_tag}"
  cpu               = 256
  memory            = 512
  desired_count     = 1
  container_port    = 8000
  vpc_id            = var.vpc_id
  cluster_id        = var.cluster_id
  security_group_id = var.security_group_id
  subnets           = var.subnets
  load_balancer_arn = var.load_balancer_arn
  Environment       = var.image_tag
  env = [
    {
      "name" : "API_KEY",
      "value" : var.api_key
    }
  ]
}
