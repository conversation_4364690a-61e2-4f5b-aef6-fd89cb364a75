terraform {
  cloud {
    organization = "propaga"
    workspaces {
      name = "propaga-develop"
    }
  }
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 4.16"
    }
    launchdarkly = {
      source  = "launchdarkly/launchdarkly"
      version = "2.24.0"
    }
  }

  required_version = ">= 1.2.0"
}

provider "aws" {
  region = "us-west-2"
}

provider "launchdarkly" {}
