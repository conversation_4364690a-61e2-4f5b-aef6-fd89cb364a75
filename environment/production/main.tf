data "aws_secretsmanager_secret_version" "production_config" {
  secret_id = "terraform/production/config"
}

locals {
  environment = "production"

  config = jsondecode(
    data.aws_secretsmanager_secret_version.production_config.secret_string
  )
}

module "vpc" {
  source = "../../network/vpc"

  vpc_name         = "${local.environment}-${local.config.vpc_name}"
  environment_name = local.environment
}

module "internet_gateway" {
  source = "../../network/internet-gateway"

  vpc_id           = module.vpc.vpc_id
  environment_name = local.environment
}

module "subnets" {
  source = "../../network/subnets"

  vpc_id = module.vpc.vpc_id
  subnets = {
    us-west-2a = "10.0.1.0/24"
    us-west-2b = "10.0.2.0/24"
    us-west-2c = "10.0.3.0/24"
  }

  internet_gateway_id = module.internet_gateway.internet_gateway_id
  environment_name    = local.environment
}

module "security_group" {
  source = "../../network/security-group"

  security_name    = "${local.environment}-${local.config.vpc_name}-sg"
  vpc_id           = module.vpc.vpc_id
  environment_name = local.environment
  open_ports = [
    443,
    3000,
    8080,
    8081,
    8082,
    80,
    5432,
    22,
    9000,
    3001,
    6379,
    8126,
    3002,
    8000,
    3003,
    3004,
    4000,
    3005,
    9513,
  ]
}

module "postgres" {
  source = "../../services/rds"

  db_password    = local.config.db_password
  db_username    = local.config.db_username
  db_name        = local.config.db_name
  instance_class = local.config.db_instance_class

  security_group_id = module.security_group.security_group_id
  subnets           = module.subnets.subnets_id

  logical_replication = true
  allocated_storage   = 20
}

module "cluster" {
  source = "../../services/ecs-cluster"

  cluster_name = "propaga-${local.environment}"
}

module "secret_manager_monoAPI" {
  source = "../../services/secret-manager"

  secret_name = "${local.environment}/mono-api/secrets"
  secrets = {
    host                     = module.postgres.rds_hostname
    username                 = module.postgres.rds_username
    password                 = module.postgres.rds_password
    gupshup_token            = local.config.monoapi_gupshup_token
    gupshup_phone_number     = local.config.monoapi_gupshup_phone_number
    google_maps_url          = local.config.monoapi_google_maps_url
    google_maps_key          = local.config.monoapi_google_maps_key
    jwt_secret_key           = local.config.monoapi_jwt_secret_key
    openpay_url              = local.config.monoapi_openpay_url
    openpay_token            = local.config.monoapi_openpay_token
    openpay_dashboard_url    = local.config.monoapi_openpay_dashboard_url
    openpay_merchant_id      = local.config.monoapi_openpay_merchant_id
    airtable_api_key         = local.config.monoapi_airtable_api_key
    airtable_base_id         = local.config.monoapi_airtable_base_id
    hilos_token              = local.config.hilos_token
    slack_url                = local.config.monoapi_slack_url
    slack_channel_id         = local.config.monoapi_slack_channel_id
    slack_token              = local.config.monoapi_slack_token
    dupplo_url               = local.config.monoapi_dupplo_url
    conekta_url              = local.config.monoapi_conekta_url
    conekta_token            = local.config.monoapi_conekta_token
    user_validator_url       = local.config.monoapi_user_validator_url
    user_validator_token     = local.config.monoapi_user_validator_token
    FACTURA_API_TOKEN_CHIPER = local.config.monoapi_FACTURA_API_TOKEN_CHIPER
    FACTURA_API_TOKEN_RABBIT = local.config.monoapi_FACTURA_API_TOKEN_RABBIT
    data_api_url             = "https://data-api.propaga.io"
    data_api_token           = local.config.datapi_api_key
    bitso_url                = local.config.monoapi_bitso_url
    bitso_api_key            = local.config.monoapi_bitso_api_key
    bitso_api_secret         = local.config.monoapi_bitso_api_secret
    algolia_app_id           = local.config.monoapi_algolia_app_id
    algolia_api_key          = local.config.monoapi_algolia_api_key
    rabbit_url               = local.config.monoapi_rabbit_url
    rabbit_token             = local.config.monoapi_rabbit_token
    make_url                 = local.config.monoapi_make_url
    pioneer_url              = local.config.monoapi_pioneer_url
    twilio_phone_number      = local.config.monoapi_twilio_phone_number
    twilio_account_sid       = local.config.monoapi_twilio_account_sid
    twilio_auth_token        = local.config.monoapi_twilio_auth_token
    zapsign_endpoint         = local.config.monoapi_zapsign_endpoint
    zapsign_token            = local.config.monoapi_zapsign_token
    rintin_url               = local.config.monoapi_rintin_url
    rintin_token             = local.config.monoapi_rintin_token
    yalo_url                 = local.config.monoapi_yalo_url
    yalo_token               = local.config.monoapi_yalo_token
    nestle_url               = local.config.monoapi_nestle_url
    nestle_token             = local.config.monoapi_nestle_token
    n8n_url                  = local.config.monoapi_n8n_url
    n8n_token                = local.config.monoapi_n8n_token
    datadog_api_key          = local.config.monoapi_datadog_api_key
    datadog_host             = local.config.monoapi_datadog_host
    trully_token             = local.config.monoapi_trully_token
    trully_host              = local.config.monoapi_trully_host
    redis_host               = module.redis.primary_endpoint_address
    redis_password           = local.config.redis_password
    clerk_url                = local.config.monoapi_clerk_url
    clerk_token              = local.config.monoapi_clerk_token
    launchdarkly_sdk_key     = local.config.monoapi_launchdarkly_sdk_key
    netpay_token             = local.config.monoapi_netpay_token
    netpay_url               = local.config.monoapi_netpay_url
    helados_holanda_url      = local.config.monoapi_helados_holanda_url
    helados_holanda_token    = local.config.monoapi_helados_holanda_token
    clerk_publishable_key    = local.config.monoapi_clerk_publishable_key
  }
}

module "secret_manager_backoffice" {
  source = "../../services/secret-manager"

  secret_name = "${local.environment}/backoffice/secrets"
  secrets = {
    host     = module.postgres.rds_hostname
    username = module.postgres.rds_username
    password = module.postgres.rds_password
  }
}

module "monoAPI" {
  source = "../../applications/mono-API"

  vpc_id            = module.vpc.vpc_id
  cluster_id        = module.cluster.cluster_id
  security_group_id = module.security_group.security_group_id
  subnets           = module.subnets.subnets_id
  load_balancer_arn = module.load_balancer.load_balancer_arn
  image_tag         = local.environment
  environment       = local.environment
}

module "webPages" {
  source = "../../applications/web-pages"

  vpc_id            = module.vpc.vpc_id
  cluster_id        = module.cluster.cluster_id
  security_group_id = module.security_group.security_group_id
  subnets           = module.subnets.subnets_id
  load_balancer_arn = module.load_balancer.load_balancer_arn
  image_tag         = local.environment
}

module "datadog" {
  source = "../../applications/datadog"

  vpc_id            = module.vpc.vpc_id
  cluster_id        = module.cluster.cluster_id
  security_group_id = module.security_group.security_group_id
  subnets           = module.subnets.subnets_id
  load_balancer_arn = module.load_balancer.load_balancer_arn
  image_tag         = local.environment
  datadog_token     = local.config.datadog_token
}


module "sentry" {
  source = "../../applications/sentry-app"

  vpc_id            = module.vpc.vpc_id
  load_balancer_arn = module.load_balancer.load_balancer_arn
}

module "n8n" {
  source = "../../applications/n8n"

  vpc_id            = module.vpc.vpc_id
  load_balancer_arn = module.load_balancer.load_balancer_arn
}

module "customerPortal" {
  source = "../../applications/customer-portal"

  vpc_id            = module.vpc.vpc_id
  cluster_id        = module.cluster.cluster_id
  security_group_id = module.security_group.security_group_id
  subnets           = module.subnets.subnets_id
  load_balancer_arn = module.load_balancer_propaga_mx.load_balancer_arn
  image_tag         = local.environment
  node_env          = "production"
}

module "dataAPI" {
  source = "../../applications/datapi"

  vpc_id            = module.vpc.vpc_id
  cluster_id        = module.cluster.cluster_id
  security_group_id = module.security_group.security_group_id
  subnets           = module.subnets.subnets_id
  load_balancer_arn = module.load_balancer.load_balancer_arn
  image_tag         = local.environment
  api_key           = local.config.datapi_api_key
}

module "backoffice" {
  source = "../../applications/backoffice"

  vpc_id            = module.vpc.vpc_id
  cluster_id        = module.cluster.cluster_id
  security_group_id = module.security_group.security_group_id
  subnets           = module.subnets.subnets_id
  load_balancer_arn = module.load_balancer_propaga_mx.load_balancer_arn
  image_tag         = local.environment
  node_env          = local.environment
}

module "pioneer" {
  source = "../../applications/pioneer"

  vpc_id            = module.vpc.vpc_id
  cluster_id        = module.cluster.cluster_id
  security_group_id = module.security_group.security_group_id
  subnets           = module.subnets.subnets_id
  load_balancer_arn = module.load_balancer_propaga_mx.load_balancer_arn
  image_tag         = local.environment
  node_env          = local.environment
  environment       = local.environment
}

module "load_balancer" {
  source = "../../services/load-balancer"

  security_group_id = module.security_group.security_group_id
  subnets           = module.subnets.subnets_id
  target_groups_domain_arns = [
    { domain = "api.propaga.io", target_group_arn = module.monoAPI.target_group_arn },
    { domain = "sentry.propaga.io", target_group_arn = module.sentry.target_group_arn },
    { domain = "dd-ingest.propaga.io", target_group_arn = module.datadog.target_group_arn },
    { domain = "terminos-condiciones.propaga.io", target_group_arn = module.webPages.target_group_arn },
    { domain = "data-api.propaga.io", target_group_arn = module.dataAPI.target_group_arn },
    { domain = "user-validation-api.propaga.io", target_group_arn = module.userValidationAPI.target_group_arn },
    { domain = "n8n.propaga.io", target_group_arn = module.n8n.target_group_arn },
  ]
  load_balancer_name = "${local.environment}-load-balancer"
  Environment        = local.environment
  certificate_arn    = "arn:aws:acm:us-west-2:014817073571:certificate/e2965284-a7f4-49ca-87f3-95a5d816d119"
}

module "load_balancer_propaga_mx" {
  source = "../../services/load-balancer"

  security_group_id = module.security_group.security_group_id
  subnets           = module.subnets.subnets_id
  target_groups_domain_arns = [
    { domain = "pagos.propaga.mx", target_group_arn = module.customerPortal.target_group_arn },
    { domain = "registro.propaga.mx", target_group_arn = module.pioneer.target_group_arn },
    { domain = "socios.propaga.mx", target_group_arn = module.kraken.target_group_arn },
    { domain = "backoffice.propaga.mx", target_group_arn = module.backoffice.target_group_arn },
  ]
  load_balancer_name = "${local.environment}-propaga-mx-lb"
  Environment        = local.environment
  certificate_arn    = "arn:aws:acm:us-west-2:014817073571:certificate/5bd9820a-a070-4b52-a83b-dd74ad125252"
}

module "file_cdn" {
  source = "../../services/cdn"

  cdn_name = "propaga-public-resources"
}

module "userValidationAPI" {
  source = "../../applications/user-validation-API"

  vpc_id            = module.vpc.vpc_id
  cluster_id        = module.cluster.cluster_id
  security_group_id = module.security_group.security_group_id
  subnets           = module.subnets.subnets_id
  load_balancer_arn = module.load_balancer.load_balancer_arn
  image_tag         = local.environment
  environment       = local.environment
}

module "secret_manager_userValidation" {
  source = "../../services/secret-manager"

  secret_name = "${local.environment}/user-validation/secrets"
  secrets = {
    phone_validator_url   = local.config.uservalidationapi_phone_validator_url
    phone_validator_token = local.config.uservalidationapi_phone_validator_token
    phone_validator_key   = local.config.uservalidationapi_phone_validator_key
    token_access          = local.config.uservalidationapi_token_access
    zenpli_url            = local.config.uservalidationapi_zenpli_url
    zenpli_client_id      = local.config.uservalidationapi_zenpli_client_id
    zenpli_client_secret  = local.config.uservalidationapi_zenpli_client_secret
    propaga_host          = local.config.uservalidationapi_propaga_host
    propaga_username      = local.config.uservalidationapi_propaga_username
    propaga_password      = local.config.uservalidationapi_propaga_password
    mongo_host            = local.config.uservalidationapi_mongo_host
  }
}

module "kraken" {
  source = "../../applications/kraken"

  vpc_id            = module.vpc.vpc_id
  cluster_id        = module.cluster.cluster_id
  security_group_id = module.security_group.security_group_id
  subnets           = module.subnets.subnets_id
  load_balancer_arn = module.load_balancer_propaga_mx.load_balancer_arn
  image_tag         = local.environment
  node_env          = local.environment
  environment       = local.environment
}

module "redis" {
  source = "../../services/redis"

  db_name           = "redis-${local.environment}"
  security_group_id = module.security_group.security_group_id
  subnets           = module.subnets.subnets_id
  db_password       = local.config.redis_password
}
