resource "aws_security_group" "security_group" {
  name_prefix = "${var.security_name}-sg"
  vpc_id      = var.vpc_id

  dynamic "ingress" {
    for_each = toset(var.open_ports)
    content {
      from_port   = ingress.value
      to_port     = ingress.value
      protocol    = "tcp"
      cidr_blocks = ["0.0.0.0/0"]
    }
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Environment = var.environment_name
  }
}
