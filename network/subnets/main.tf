resource "aws_route_table" "public" {
  vpc_id = var.vpc_id
  count  = 3
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = var.internet_gateway_id
  }
}

resource "aws_subnet" "subnet" {
  count                   = 3
  cidr_block              = "10.0.${count.index}.0/24"
  vpc_id                  = var.vpc_id
  availability_zone       = element(keys(var.subnets), count.index)
  map_public_ip_on_launch = true

  tags = {
    Name        = "${var.environment_name}-subnet-${count.index + 1}"
    Environment = var.environment_name
  }
}

resource "aws_route_table_association" "subnet1" {
  subnet_id      = aws_subnet.subnet[0].id
  route_table_id = aws_route_table.public[0].id
}

resource "aws_route_table_association" "subnet2" {
  subnet_id      = aws_subnet.subnet[1].id
  route_table_id = aws_route_table.public[1].id
}

resource "aws_route_table_association" "subnet3" {
  subnet_id      = aws_subnet.subnet[2].id
  route_table_id = aws_route_table.public[2].id
}