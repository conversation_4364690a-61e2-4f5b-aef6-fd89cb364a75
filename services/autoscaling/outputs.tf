output "autoscaling_target_resource_id" {
  description = "Auto-scaling target resource ID"
  value       = aws_appautoscaling_target.ecs_target.resource_id
}

output "autoscaling_target_arn" {
  description = "Auto-scaling target ARN"
  value       = aws_appautoscaling_target.ecs_target.arn
}

output "cpu_policy_arn" {
  description = "CPU scaling policy ARN"
  value       = var.enable_cpu_scaling ? aws_appautoscaling_policy.ecs_cpu_policy[0].arn : null
}

output "cpu_policy_name" {
  description = "CPU scaling policy name"
  value       = var.enable_cpu_scaling ? aws_appautoscaling_policy.ecs_cpu_policy[0].name : null
}

output "memory_policy_arn" {
  description = "Memory scaling policy ARN"
  value       = var.enable_memory_scaling ? aws_appautoscaling_policy.ecs_memory_policy[0].arn : null
}

output "memory_policy_name" {
  description = "Memory scaling policy name"
  value       = var.enable_memory_scaling ? aws_appautoscaling_policy.ecs_memory_policy[0].name : null
}

output "min_capacity" {
  description = "Minimum capacity configured for auto-scaling"
  value       = var.min_capacity
}

output "max_capacity" {
  description = "Maximum capacity configured for auto-scaling"
  value       = var.max_capacity
}

output "cpu_target_value" {
  description = "CPU target utilization percentage"
  value       = var.cpu_target_value
}

output "memory_target_value" {
  description = "Memory target utilization percentage"
  value       = var.memory_target_value
}
