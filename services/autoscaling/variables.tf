variable "cluster_name" {
  description = "Name of the ECS cluster"
  type        = string
}

variable "service_name" {
  description = "Name of the ECS service"
  type        = string
}

variable "min_capacity" {
  description = "Minimum number of tasks for auto-scaling"
  type        = number
  default     = 1
  validation {
    condition     = var.min_capacity >= 1
    error_message = "Minimum capacity must be at least 1."
  }
}

variable "max_capacity" {
  description = "Maximum number of tasks for auto-scaling"
  type        = number
  default     = 10
  validation {
    condition     = var.max_capacity >= var.min_capacity
    error_message = "Maximum capacity must be greater than or equal to minimum capacity."
  }
}

variable "cpu_target_value" {
  description = "Target CPU utilization percentage for auto-scaling"
  type        = number
  default     = 70
  validation {
    condition     = var.cpu_target_value > 0 && var.cpu_target_value <= 100
    error_message = "CPU target value must be between 1 and 100."
  }
}

variable "memory_target_value" {
  description = "Target memory utilization percentage for auto-scaling"
  type        = number
  default     = 80
  validation {
    condition     = var.memory_target_value > 0 && var.memory_target_value <= 100
    error_message = "Memory target value must be between 1 and 100."
  }
}

variable "scale_out_cooldown" {
  description = "Cooldown period (in seconds) after a scale-out activity"
  type        = number
  default     = 300
  validation {
    condition     = var.scale_out_cooldown >= 60
    error_message = "Scale out cooldown must be at least 60 seconds."
  }
}

variable "scale_in_cooldown" {
  description = "Cooldown period (in seconds) after a scale-in activity"
  type        = number
  default     = 300
  validation {
    condition     = var.scale_in_cooldown >= 60
    error_message = "Scale in cooldown must be at least 60 seconds."
  }
}

variable "enable_cpu_scaling" {
  description = "Enable CPU-based auto-scaling policy"
  type        = bool
  default     = true
}

variable "enable_memory_scaling" {
  description = "Enable memory-based auto-scaling policy"
  type        = bool
  default     = true
}

variable "policy_name_prefix" {
  description = "Prefix for auto-scaling policy names"
  type        = string
  default     = ""
}

variable "tags" {
  description = "A map of tags to assign to the resources"
  type        = map(string)
  default     = {}
}
