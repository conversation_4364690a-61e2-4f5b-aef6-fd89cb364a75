# Integration Example: How to add autoscaling to existing applications
# This file demonstrates how to use the autoscaling module with existing ECS services

# Example: Adding autoscaling to the Pioneer application
# This would be added to applications/pioneer/main.tf

/*
# Existing ECS service (already in pioneer/main.tf)
module "ecs" {
  source = "../../services/ecs"

  app_name          = "pioner"
  image_repo_url    = "014817073571.dkr.ecr.us-west-2.amazonaws.com/pioneer:${var.image_tag}"
  cpu               = 256
  memory            = 512
  desired_count     = 1
  container_port    = 3004
  vpc_id            = var.vpc_id
  cluster_id        = var.cluster_id
  security_group_id = var.security_group_id
  subnets           = var.subnets
  load_balancer_arn = var.load_balancer_arn
  Environment       = var.environment
  env = [
    {
      "name" : "TZ",
      "value" : "UTC-6"
    },
    {
      "name" : "NODE_ENV",
      "value" : var.node_env
    },
  ]
}

# NEW: Add autoscaling using the standalone autoscaling module
module "pioneer_autoscaling" {
  source = "../../services/autoscaling"

  cluster_name       = var.cluster_id
  service_name       = module.ecs.ecs_service_name
  min_capacity       = 1
  max_capacity       = 5
  cpu_target_value   = 70
  memory_target_value = 80
  
  tags = {
    Environment = var.environment
    Application = "pioneer"
  }

  depends_on = [module.ecs]
}
*/

# Example: Adding autoscaling to Products Web application
/*
# This would be added to applications/products-web/main.tf

module "ecs" {
  source = "../../services/ecs-spot"
  # ... existing configuration
}

# NEW: Add autoscaling
module "products_web_autoscaling" {
  source = "../../services/autoscaling"

  cluster_name       = var.cluster_id
  service_name       = module.ecs.ecs_service_name
  min_capacity       = 1
  max_capacity       = 8
  cpu_target_value   = 65
  memory_target_value = 75
  
  tags = {
    Environment = var.image_tag
    Application = "products-web"
  }

  depends_on = [module.ecs]
}
*/

# Example: Adding autoscaling to Docs API application
/*
# This would be added to applications/docs-API/main.tf

module "ecs" {
  source = "../../services/ecs"
  # ... existing configuration
}

# NEW: Add autoscaling
module "docs_api_autoscaling" {
  source = "../../services/autoscaling"

  cluster_name       = var.cluster_id
  service_name       = module.ecs.ecs_service_name
  min_capacity       = 1
  max_capacity       = 6
  cpu_target_value   = 75
  memory_target_value = 80
  
  tags = {
    Environment = var.image_tag
    Application = "docs-api"
  }

  depends_on = [module.ecs]
}
*/
