# ECS Auto-scaling Module

This Terraform module provides auto-scaling configuration for Amazon ECS services. It creates auto-scaling targets and policies for CPU and memory utilization.

## Features

- **Auto-scaling Target**: Configures the scalable target for ECS services
- **CPU-based Scaling**: Target tracking scaling policy based on CPU utilization
- **Memory-based Scaling**: Target tracking scaling policy based on memory utilization
- **Flexible Configuration**: Customizable scaling parameters and cooldown periods
- **Optional Policies**: Enable/disable CPU or memory scaling independently

## Usage

### Basic Usage

```hcl
module "autoscaling" {
  source = "../../services/autoscaling"

  cluster_name = "my-cluster"
  service_name = "my-service"
  min_capacity = 1
  max_capacity = 10
}
```

### Advanced Usage

```hcl
module "autoscaling" {
  source = "../../services/autoscaling"

  cluster_name         = "production-cluster"
  service_name         = "api-service"
  min_capacity         = 2
  max_capacity         = 20
  cpu_target_value     = 60
  memory_target_value  = 75
  scale_out_cooldown   = 180
  scale_in_cooldown    = 300
  enable_cpu_scaling   = true
  enable_memory_scaling = true
  policy_name_prefix   = "prod-"

  tags = {
    Environment = "production"
    Team        = "platform"
  }
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.2.0 |
| aws | ~> 4.16 |

## Providers

| Name | Version |
|------|---------|
| aws | ~> 4.16 |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| cluster_name | Name of the ECS cluster | `string` | n/a | yes |
| service_name | Name of the ECS service | `string` | n/a | yes |
| min_capacity | Minimum number of tasks for auto-scaling | `number` | `1` | no |
| max_capacity | Maximum number of tasks for auto-scaling | `number` | `10` | no |
| cpu_target_value | Target CPU utilization percentage for auto-scaling | `number` | `70` | no |
| memory_target_value | Target memory utilization percentage for auto-scaling | `number` | `80` | no |
| scale_out_cooldown | Cooldown period (in seconds) after a scale-out activity | `number` | `300` | no |
| scale_in_cooldown | Cooldown period (in seconds) after a scale-in activity | `number` | `300` | no |
| enable_cpu_scaling | Enable CPU-based auto-scaling policy | `bool` | `true` | no |
| enable_memory_scaling | Enable memory-based auto-scaling policy | `bool` | `true` | no |
| policy_name_prefix | Prefix for auto-scaling policy names | `string` | `""` | no |
| tags | A map of tags to assign to the resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| autoscaling_target_resource_id | Auto-scaling target resource ID |
| autoscaling_target_arn | Auto-scaling target ARN |
| cpu_policy_arn | CPU scaling policy ARN |
| cpu_policy_name | CPU scaling policy name |
| memory_policy_arn | Memory scaling policy ARN |
| memory_policy_name | Memory scaling policy name |
| min_capacity | Minimum capacity configured for auto-scaling |
| max_capacity | Maximum capacity configured for auto-scaling |
| cpu_target_value | CPU target utilization percentage |
| memory_target_value | Memory target utilization percentage |

## Notes

- This module is designed to work with existing ECS services
- The ECS service must exist before applying this module
- Ensure that the cluster and service names are correct
- The module supports both CPU and memory-based scaling policies
- Cooldown periods help prevent rapid scaling actions
