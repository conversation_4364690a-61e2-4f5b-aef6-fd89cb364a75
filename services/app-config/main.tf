resource "aws_appconfig_application" "application" {
  name        = var.feature_flag_name
  description = var.feature_flag_description
}

resource "aws_appconfig_configuration_profile" "profile" {
  name           = "${var.feature_flag_name}-profile"
  application_id = aws_appconfig_application.application.id
  description    = var.feature_flag_description
  location_uri   = "hosted"
}

resource "aws_appconfig_environment" "environment" {
  application_id = aws_appconfig_application.application.id
  name           = var.environment_name
  description    = var.feature_flag_description
}

resource "aws_appconfig_deployment_strategy" "strategy" {
  name                           = "${var.feature_flag_name}-strategy"
  description                    = var.feature_flag_description
  deployment_duration_in_minutes = var.deployment_duration_in_minutes
  growth_type                    = var.growth_type
  growth_factor                  = 10
  final_bake_time_in_minutes     = 5
  replicate_to                   = "NONE"
}

resource "aws_appconfig_deployment" "deployment" {
  environment_id           = aws_appconfig_environment.environment.environment_id
  configuration_version    = var.configuration_version
  application_id           = aws_appconfig_application.application.id
  configuration_profile_id = aws_appconfig_configuration_profile.profile.configuration_profile_id
  deployment_strategy_id   = aws_appconfig_deployment_strategy.strategy.id
  description              = var.feature_flag_description
}

resource "aws_appconfig_hosted_configuration_version" "config_version" {
  application_id           = aws_appconfig_application.application.id
  content_type             = "application/json"
  configuration_profile_id = aws_appconfig_configuration_profile.profile.configuration_profile_id
  content                  = jsonencode(var.content)
}
