variable "feature_flag_name" {
  type        = string
  description = "Feature flag name"

}

variable "feature_flag_description" {
  type        = string
  description = "Feature flag description"
}

variable "environment_name" {
  type        = string
  description = "Environment name"
}

variable "deployment_duration_in_minutes" {
  type        = number
  description = "Deployment duration"
  default     = 5
}

variable "growth_type" {
  type        = string
  description = "Deployment type (LINEAR or EXPONENTIAL)"
  default     = "LINEAR"
}

variable "configuration_version" {
  type        = number
  description = "Version to force a new deployment"
}

variable "content" {
  type      = map(bool)
  sensitive = true
}
