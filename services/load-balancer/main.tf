resource "aws_lb" "load_balancer" {
  name               = var.load_balancer_name
  internal           = false
  load_balancer_type = "application"
  security_groups    = [var.security_group_id]
  subnets            = var.subnets

  enable_deletion_protection = true

  tags = {
    Environment = var.Environment
  }
}

resource "aws_lb_listener" "https_redirect" {
  load_balancer_arn = aws_lb.load_balancer.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type = "redirect"

    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}

resource "aws_lb_listener" "listener" {
  load_balancer_arn = aws_lb.load_balancer.arn
  port              = 443
  protocol          = "HTTPS"

  default_action {
    type = "redirect"

    redirect {
      protocol    = "HTTPS"
      status_code = "HTTP_301"
      host        = "pagos.propaga.mx"
    }
  }

  ssl_policy      = "ELBSecurityPolicy-2016-08"
  certificate_arn = var.certificate_arn
}

resource "aws_lb_listener_rule" "domains" {
  for_each = { for idx, obj in var.target_groups_domain_arns : idx => obj }

  listener_arn = aws_lb_listener.listener.arn

  action {
    type             = "forward"
    target_group_arn = each.value["target_group_arn"]
  }

  condition {
    host_header {
      values = [each.value["domain"]]
    }
  }
}
