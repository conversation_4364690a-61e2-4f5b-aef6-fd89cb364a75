# Application Load Balancer configured for optimal ECS task distribution
# Uses round-robin algorithm (default) for even traffic distribution across all healthy targets
resource "aws_lb" "load_balancer" {
  name               = var.load_balancer_name
  internal           = false
  load_balancer_type = "application"
  security_groups    = [var.security_group_id]
  subnets            = var.subnets

  enable_deletion_protection = true

  # Enable cross-zone load balancing for better distribution across AZs
  # This ensures traffic is distributed evenly across all Availability Zones
  # Default: true (can be overridden via variable for retro-compatibility)
  enable_cross_zone_load_balancing = var.enable_cross_zone_load_balancing

  tags = {
    Environment = var.Environment
  }
}

resource "aws_lb_listener" "https_redirect" {
  load_balancer_arn = aws_lb.load_balancer.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type = "redirect"

    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}

# HTTPS Listener with enhanced load distribution strategy
# Maintains retro-compatibility while enabling proper load balancing
resource "aws_lb_listener" "listener" {
  load_balancer_arn = aws_lb.load_balancer.arn
  port              = 443
  protocol          = "HTTPS"

  # Default action: Forward to default target group if provided, otherwise maintain original redirect behavior
  default_action {
    type = var.default_target_group_arn != null ? "forward" : "redirect"

    # Forward action for proper load distribution (when default_target_group_arn is provided)
    dynamic "forward" {
      for_each = var.default_target_group_arn != null ? [1] : []
      content {
        target_group {
          arn = var.default_target_group_arn
          # Round-robin distribution with equal weight
          weight = 100
        }

        # Enable sticky sessions if configured
        dynamic "stickiness" {
          for_each = var.enable_sticky_sessions ? [1] : []
          content {
            enabled  = true
            duration = var.sticky_session_duration
          }
        }
      }
    }

    # Maintain original redirect behavior for retro-compatibility (when no default target group)
    dynamic "redirect" {
      for_each = var.default_target_group_arn == null ? [1] : []
      content {
        protocol    = "HTTPS"
        status_code = "HTTP_301"
        host        = "pagos.propaga.mx"
      }
    }
  }

  # Updated SSL policy for better security (configurable for retro-compatibility)
  ssl_policy      = var.ssl_policy
  certificate_arn = var.certificate_arn
}

# Listener rules for domain-based routing with optimized load distribution
# Each rule forwards traffic to specific target groups based on host headers
# Uses round-robin algorithm for even distribution across ECS tasks
resource "aws_lb_listener_rule" "domains" {
  for_each = { for idx, obj in var.target_groups_domain_arns : idx => obj }

  listener_arn = aws_lb_listener.listener.arn
  priority     = 100 + each.key # Ensure consistent priority ordering

  # Enhanced forward action with proper load balancing configuration
  action {
    type = "forward"

    # Use new forward block syntax for better load distribution control
    forward {
      target_group {
        arn = each.value["target_group_arn"]
        # Equal weight for round-robin distribution across all targets
        weight = 100
      }

      # Enable sticky sessions if configured globally
      dynamic "stickiness" {
        for_each = var.enable_sticky_sessions ? [1] : []
        content {
          enabled  = true
          duration = var.sticky_session_duration
        }
      }
    }
  }

  # Route based on host header (domain)
  condition {
    host_header {
      values = [each.value["domain"]]
    }
  }
}

# ============================================================================
# LOAD DISTRIBUTION STRATEGY DOCUMENTATION
# ============================================================================
#
# This load balancer implements the following load distribution strategy:
#
# 1. ROUND-ROBIN ALGORITHM:
#    - AWS ALB uses round-robin by default for even traffic distribution
#    - Each ECS task receives requests in sequential order
#    - Ensures balanced load across all healthy targets
#    - Weight = 100 for all target groups ensures equal distribution
#
# 2. CROSS-ZONE LOAD BALANCING:
#    - Enabled by default (configurable via enable_cross_zone_load_balancing)
#    - Distributes traffic evenly across all Availability Zones
#    - Prevents hotspots in specific AZs
#    - Improves fault tolerance and performance
#
# 3. RETRO-COMPATIBILITY:
#    - All new variables have safe defaults
#    - Original redirect behavior maintained when default_target_group_arn is null
#    - SSL policy configurable (defaults to modern TLS 1.2)
#    - Cross-zone load balancing can be disabled if needed
#
# 4. STICKY SESSIONS (OPTIONAL):
#    - Disabled by default (enable_sticky_sessions = false)
#    - Can be enabled for stateful applications
#    - Duration configurable (default: 24 hours)
#    - Applied to both default action and listener rules
#
# 5. HEALTH CHECK INTEGRATION:
#    - Works with existing target group health checks
#    - Round-robin only routes to healthy targets
#    - Automatic failover to healthy tasks
#    - Supports ECS service rolling deployments
#
# 6. USAGE EXAMPLES:
#
#    # Retro-compatible (no changes needed):
#    module "load_balancer" {
#      source = "../../services/load-balancer"
#      # ... existing variables only
#    }
#
#    # Enhanced load balancing:
#    module "load_balancer" {
#      source = "../../services/load-balancer"
#      # ... existing variables ...
#      default_target_group_arn = module.main_app.target_group_arn
#      enable_sticky_sessions   = false
#    }
#
# ============================================================================
