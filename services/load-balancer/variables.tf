variable "security_group_id" {
  type        = string
  description = "Security group id"
}

variable "subnets" {
  type        = list(any)
  description = "Subnets list"
}

variable "load_balancer_name" {
  description = "Load balancer name"
  type        = string
}

variable "Environment" {
  description = "Environment variables"
  type        = string
}

variable "target_groups_domain_arns" {
  description = "Mapper to target group and domains"
  type = list(object({
    domain           = string
    target_group_arn = string
  }))
}

variable "certificate_arn" {
  description = "Certificate ARN"
  type        = string
}
