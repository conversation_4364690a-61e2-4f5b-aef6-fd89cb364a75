variable "security_group_id" {
  type        = string
  description = "Security group id"
}

variable "subnets" {
  type        = list(any)
  description = "Subnets list"
}

variable "load_balancer_name" {
  description = "Load balancer name"
  type        = string
}

variable "Environment" {
  description = "Environment variables"
  type        = string
}

variable "target_groups_domain_arns" {
  description = "Mapper to target group and domains"
  type = list(object({
    domain           = string
    target_group_arn = string
  }))
}

variable "certificate_arn" {
  description = "Certificate ARN"
  type        = string
}

# Optional variables for enhanced load balancing (retro-compatible)
variable "default_target_group_arn" {
  description = "Default target group ARN for load balancer default action (optional for retro-compatibility)"
  type        = string
  default     = null
}

variable "enable_sticky_sessions" {
  description = "Enable sticky sessions for load balancing"
  type        = bool
  default     = false
}

variable "sticky_session_duration" {
  description = "Duration for sticky sessions in seconds (1-604800)"
  type        = number
  default     = 86400
  validation {
    condition     = var.sticky_session_duration >= 1 && var.sticky_session_duration <= 604800
    error_message = "Sticky session duration must be between 1 and 604800 seconds (7 days)."
  }
}

variable "enable_cross_zone_load_balancing" {
  description = "Enable cross-zone load balancing for better distribution across AZs"
  type        = bool
  default     = true
}

variable "ssl_policy" {
  description = "SSL policy for HTTPS listener"
  type        = string
  default     = "ELBSecurityPolicy-TLS-1-2-2017-01"
}
