variable "db_username" {
  type        = string
  description = "Username of database"
}

variable "db_name" {
  description = "DB name"
  type        = string
}

variable "db_password" {
  description = "RDS root user password"
  type        = string
  sensitive   = true
}

variable "security_group_id" {
  type        = string
  description = "Security group id"
}

variable "subnets" {
  type        = list(any)
  description = "Subnets id list"
}

variable "instance_class" {
  type        = string
  description = "Database intance class"
  default     = "db.t3.micro"
}

variable "allocated_storage" {
  type        = number
  description = "Allocated storage"
  default     = 5
}

variable "logical_replication" {
  type        = bool
  description = "Enable logical replication"
  default     = false
}

