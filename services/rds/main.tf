resource "aws_db_parameter_group" "parameter_group" {
  name   = var.db_name
  family = "postgres14"

  parameter {
    name  = "log_connections"
    value = "1"
  }

  parameter {
    name  = "timezone"
    value = "America/Mexico_City"
  }

  parameter {
    name         = "rds.logical_replication"
    value        = var.logical_replication ? "1" : "0"
    apply_method = "pending-reboot"
  }

  parameter {
    name         = "wal_sender_timeout"
    value        = "30000"
    apply_method = "pending-reboot"
  }

  parameter {
    name         = "max_wal_senders"
    value        = "5"
    apply_method = "pending-reboot"
  }

  parameter {
    name         = "max_worker_processes"
    value        = "5"
    apply_method = "pending-reboot"
  }
  parameter {
    name         = "max_wal_size"
    value        = "2048"
    apply_method = "pending-reboot"
  }
  parameter {
    name         = "max_slot_wal_keep_size"
    value        = "2048"
    apply_method = "pending-reboot"
  }
}

resource "aws_db_subnet_group" "subnet_group" {
  name       = var.db_name
  subnet_ids = var.subnets
}

resource "aws_db_instance" "database" {
  identifier                   = var.db_name
  instance_class               = var.instance_class
  allocated_storage            = var.allocated_storage
  engine                       = "postgres"
  engine_version               = "14.17"
  username                     = var.db_username
  password                     = var.db_password
  db_subnet_group_name         = aws_db_subnet_group.subnet_group.name
  vpc_security_group_ids       = [var.security_group_id]
  parameter_group_name         = aws_db_parameter_group.parameter_group.name
  publicly_accessible          = true
  skip_final_snapshot          = true
  performance_insights_enabled = true
  backup_retention_period      = 7
  backup_window                = "05:00-07:00"
}
