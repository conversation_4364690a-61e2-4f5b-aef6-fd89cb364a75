variable "db_name" {
  description = "Redis name"
  type        = string
}

variable "node_type" {
  description = "instance type"
  type        = string
  default     = "cache.t3.micro"
}

variable "subnets" {
  type        = list(any)
  description = "Subnets id list"
}

variable "db_password" {
  description = "DB secret password"
  sensitive   = true
  type        = string
}

variable "security_group_id" {
  type        = string
  description = "Security group id"
}
