resource "aws_elasticache_subnet_group" "subnets" {
  name       = "${var.db_name}-subnet-group"
  subnet_ids = var.subnets
}

resource "aws_elasticache_replication_group" "replication_group" {
  replication_group_id = var.db_name
  description          = "${var.db_name} replication group"
  node_type            = var.node_type
  engine               = "redis"
  apply_immediately    = true

  automatic_failover_enabled = true
  num_node_groups            = 1
  replicas_per_node_group    = 1

  subnet_group_name    = aws_elasticache_subnet_group.subnets.name
  security_group_ids   = [var.security_group_id]
  parameter_group_name = "default.redis7"

  auth_token                 = var.db_password
  transit_encryption_enabled = true
  port                       = 6379
}
