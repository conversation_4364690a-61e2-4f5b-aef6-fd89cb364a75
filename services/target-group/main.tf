resource "aws_lb_target_group" "target_group" {
  name_prefix = var.app_name
  port        = var.port
  protocol    = "HTTP"
  target_type = "instance"
  vpc_id      = var.vpc_id

  health_check {
    healthy_threshold   = 2
    interval            = 30
    path                = "/"
    port                = var.port
    protocol            = "HTTP"
    timeout             = 5
    unhealthy_threshold = 2
    matcher             = "200-499"
  }
}