variable "app_name" {
  type        = string
  description = "App name"
}

variable "image_repo_url" {
  type        = string
  description = "ECR image url"
}

variable "cpu" {
  type        = number
  description = "CPU reserved to image"
}

variable "memory" {
  type        = number
  description = "Memory reserved to use ECS"
}

variable "desired_count" {
  type        = number
  description = "Desired instance number"
}

variable "container_port" {
  type        = number
  description = "Container port"
}

variable "vpc_id" {
  type        = string
  description = "VPC id"
}

variable "cluster_id" {
  type        = string
  description = "Cluster id"
}

variable "security_group_id" {
  type        = string
  description = "Security group id"
}

variable "subnets" {
  type        = list(any)
  description = "Subnets id list"
}

variable "load_balancer_arn" {
  type        = string
  description = "Load balancer arn"
}

variable "env" {
  type        = list(any)
  description = "Environment variables"
  default     = []
}

variable "Environment" {
  description = "Environment variables"
  type        = string
}

# Auto-scaling variables
variable "enable_autoscaling" {
  description = "Enable auto-scaling for the ECS service"
  type        = bool
  default     = false
}

variable "autoscaling_min_capacity" {
  description = "Minimum number of tasks for auto-scaling"
  type        = number
  default     = 1
}

variable "autoscaling_max_capacity" {
  description = "Maximum number of tasks for auto-scaling"
  type        = number
  default     = 10
}

variable "autoscaling_cpu_target" {
  description = "Target CPU utilization percentage for auto-scaling"
  type        = number
  default     = 70
}

variable "autoscaling_memory_target" {
  description = "Target memory utilization percentage for auto-scaling"
  type        = number
  default     = 80
}

variable "autoscaling_scale_out_cooldown" {
  description = "Cooldown period (in seconds) after a scale-out activity"
  type        = number
  default     = 300
}

variable "autoscaling_scale_in_cooldown" {
  description = "Cooldown period (in seconds) after a scale-in activity"
  type        = number
  default     = 300
}

